version: '3.8'

services:
  # VSCode Self-Host Code Server
  code-server:
    image: codercom/code-server:latest
    container_name: vscode-selfhost
    restart: unless-stopped
    environment:
      - PASSWORD=${PASSWORD:-admin123}
      - SUDO_PASSWORD=${SUDO_PASSWORD:-admin123}
      - PROXY_DOMAIN=${PROXY_DOMAIN:-acb.xyz}
      # Microsoft Marketplace Configuration
      - >-
        EXTENSIONS_GALLERY={"serviceUrl": "https://marketplace.visualstudio.com/_apis/public/gallery", "cacheUrl": "https://vscode.blob.core.windows.net/gallery/index", "itemUrl": "https://marketplace.visualstudio.com/items", "controlUrl": "", "recommendationsUrl": ""}
      - SERVICE_URL=https://marketplace.visualstudio.com/_apis/public/gallery
      - ITEM_URL=https://marketplace.visualstudio.com/items
    volumes:
      - ./config:/home/<USER>/.config
      - ./data:/home/<USER>/workspace
      - ./extensions:/home/<USER>/.local/share/code-server/extensions
      - /var/run/docker.sock:/var/run/docker.sock:ro
    user: "1000:1000"
    command: >
      --bind-addr 0.0.0.0:8080
      --user-data-dir /home/<USER>/.config
      --auth password
      --disable-telemetry
      --extensions-dir /home/<USER>/.local/share/code-server/extensions
      --enable-proposed-api GitHub.copilot
      --enable-proposed-api GitHub.copilot-chat
      --proxy-domain ${PROXY_DOMAIN:-acb.xyz}
    networks:
      - vscode-network
    # Only expose to internal network, not to host
    expose:
      - "8080"

  # Caddy Reverse Proxy with Automatic HTTPS
  caddy:
    image: caddy:2-alpine
    container_name: vscode-caddy
    restart: unless-stopped
    ports:
      - "80:80"
      - "443:443"
    environment:
      - DOMAIN=${DOMAIN:-acb.xyz}
      - EMAIL=${EMAIL:-<EMAIL>}
    volumes:
      - ./caddy/Caddyfile:/etc/caddy/Caddyfile:ro
      - ./caddy/data:/data
      - ./caddy/config:/config
      - ./caddy/logs:/var/log/caddy
    networks:
      - vscode-network
    depends_on:
      - code-server

  # Nginx Alternative (comment out caddy and uncomment this if you prefer nginx)
  # nginx:
  #   image: nginx:alpine
  #   container_name: vscode-nginx
  #   restart: unless-stopped
  #   ports:
  #     - "80:80"
  #     - "443:443"
  #   volumes:
  #     - ./nginx/nginx.conf:/etc/nginx/nginx.conf:ro
  #     - ./nginx/ssl:/etc/nginx/ssl:ro
  #     - ./nginx/logs:/var/log/nginx
  #   networks:
  #     - vscode-network
  #   depends_on:
  #     - code-server

networks:
  vscode-network:
    driver: bridge

volumes:
  config:
  data:
  extensions:
