# Cấu hình HTTPS cho VSCode Self-Host

## Tổng quan

Hướng dẫn này sẽ giúp bạn cấu hình HTTPS cho VSCode Self-Host với domain `acb.xyz` và SSL certificate an toàn.

## Phương pháp cấu hình

### 🚀 Phương pháp 1: Docker Compose với <PERSON>dy (Khuyến nghị)

**Ưu điểm:**
- ✅ Tự động quản lý SSL certificate
- ✅ Dễ dàng triển khai và quản lý
- ✅ Tự động gia hạn certificate
- ✅ Cấu hình bảo mật tối ưu

**Cách sử dụng:**
```bash
# Cấu hình HTTPS với Docker
./scripts/https-manager.sh
# Chọn option 2: "Cấu hình HTTPS với Docker"
```

### 🔧 Phương pháp 2: System Services

#### A. Caddy System Service
```bash
# Cài đặt và cấu hình <PERSON>
./scripts/setup-https-caddy.sh acb.xyz <EMAIL>
```

#### B. Nginx + Certbot
```bash
# Cài đặt và cấu hình Nginx với Let's Encrypt
./scripts/setup-https-nginx.sh acb.xyz <EMAIL>
```

## Cấu hình chi tiết

### Docker Compose HTTPS Stack

File `docker-compose.https.yml` bao gồm:

1. **Code-Server Container**
   - Chạy VSCode Self-Host
   - Chỉ expose port nội bộ
   - Cấu hình Microsoft marketplace

2. **Caddy Reverse Proxy**
   - Tự động SSL với Let's Encrypt
   - Security headers
   - WebSocket support
   - Compression và caching

### Environment Variables

Tạo file `.env`:
```bash
DOMAIN=acb.xyz
EMAIL=<EMAIL>
PASSWORD=your-secure-password
SUDO_PASSWORD=your-sudo-password
PROXY_DOMAIN=acb.xyz
```

### Caddyfile Configuration

```caddy
acb.xyz {
    reverse_proxy code-server:8080 {
        header_up Upgrade {http.request.header.Upgrade}
        header_up Connection {http.request.header.Connection}
        header_up Host {http.request.host}
        header_up X-Real-IP {http.request.remote.host}
        header_up X-Forwarded-For {http.request.remote.host}
        header_up X-Forwarded-Proto {http.request.scheme}
    }
    
    header {
        Strict-Transport-Security "max-age=31536000; includeSubDomains; preload"
        X-Frame-Options "SAMEORIGIN"
        X-Content-Type-Options "nosniff"
        X-XSS-Protection "1; mode=block"
        Referrer-Policy "strict-origin-when-cross-origin"
    }
    
    tls <EMAIL>
}

http://acb.xyz {
    redir https://{host}{uri} permanent
}
```

## Yêu cầu hệ thống

### DNS Configuration
```bash
# Đảm bảo DNS record trỏ về server
acb.xyz.    A    YOUR_SERVER_IP
```

### Firewall Configuration
```bash
# Mở ports cần thiết
sudo ufw allow 80/tcp   # HTTP (cho Let's Encrypt)
sudo ufw allow 443/tcp  # HTTPS
sudo ufw allow 22/tcp   # SSH
```

### Domain Requirements
- Domain `acb.xyz` phải trỏ về server của bạn
- Port 80 và 443 phải accessible từ internet
- Không có firewall block Let's Encrypt validation

## Quản lý SSL Certificates

### Automatic Renewal (Caddy)
Caddy tự động gia hạn certificates, không cần can thiệp thủ công.

### Manual Management (Nginx + Certbot)
```bash
# Xem certificates hiện tại
sudo certbot certificates

# Gia hạn thủ công
sudo certbot renew

# Test gia hạn
sudo certbot renew --dry-run

# Backup certificates
sudo tar -czf ssl-backup.tar.gz /etc/letsencrypt
```

## Troubleshooting

### 1. Certificate không được tạo

**Nguyên nhân thường gặp:**
- DNS chưa trỏ đúng về server
- Port 80/443 bị block
- Domain không accessible từ internet

**Giải pháp:**
```bash
# Kiểm tra DNS
nslookup acb.xyz

# Kiểm tra port connectivity
nc -z acb.xyz 80
nc -z acb.xyz 443

# Kiểm tra từ bên ngoài
curl -I http://acb.xyz
```

### 2. WebSocket connection issues

**Giải pháp:**
Đảm bảo reverse proxy có cấu hình WebSocket headers:
```
Upgrade: websocket
Connection: upgrade
```

### 3. Mixed content warnings

**Giải pháp:**
- Đảm bảo tất cả resources load qua HTTPS
- Kiểm tra Content Security Policy
- Verify security headers

## Security Best Practices

### 1. Security Headers
```
Strict-Transport-Security: max-age=31536000; includeSubDomains; preload
X-Frame-Options: SAMEORIGIN
X-Content-Type-Options: nosniff
X-XSS-Protection: 1; mode=block
Referrer-Policy: strict-origin-when-cross-origin
```

### 2. SSL Configuration
- Sử dụng TLS 1.2+ only
- Strong cipher suites
- HSTS enabled
- Certificate transparency

### 3. Access Control
```bash
# Restrict access by IP (optional)
# Trong Caddyfile:
@allowed {
    remote_ip ***********/24 10.0.0.0/8
}
handle @allowed {
    reverse_proxy code-server:8080
}
handle {
    respond "Access Denied" 403
}
```

## Monitoring và Logging

### Caddy Logs
```bash
# Docker logs
docker logs -f vscode-caddy

# System service logs
sudo journalctl -u caddy -f
```

### Nginx Logs
```bash
# Access logs
sudo tail -f /var/log/nginx/vscode-selfhost.access.log

# Error logs
sudo tail -f /var/log/nginx/vscode-selfhost.error.log
```

### SSL Certificate Monitoring
```bash
# Check certificate expiry
echo | openssl s_client -servername acb.xyz -connect acb.xyz:443 2>/dev/null | openssl x509 -noout -dates
```

## Backup và Recovery

### Backup Configuration
```bash
# Backup Caddy config
tar -czf caddy-backup.tar.gz ./caddy/

# Backup Nginx config
sudo tar -czf nginx-backup.tar.gz /etc/nginx/sites-available/vscode-selfhost

# Backup SSL certificates
sudo tar -czf ssl-backup.tar.gz /etc/letsencrypt/
```

### Recovery Process
1. Restore configuration files
2. Restart services
3. Verify SSL certificates
4. Test HTTPS connectivity

## Performance Optimization

### 1. Enable Compression
```caddy
encode {
    gzip 6
    zstd
    minimum_length 1024
}
```

### 2. Caching Headers
```caddy
@static {
    file
    path *.css *.js *.png *.jpg *.jpeg *.gif *.ico *.svg
}
handle @static {
    header Cache-Control "public, max-age=31536000"
    file_server
}
```

### 3. Rate Limiting
```caddy
rate_limit {
    zone static_files {
        key {remote_host}
        events 100
        window 1m
    }
}
```

## Scripts và Tools

- `scripts/https-manager.sh` - Tool quản lý HTTPS tổng hợp
- `scripts/setup-https-caddy.sh` - Cài đặt Caddy system service
- `scripts/setup-https-nginx.sh` - Cài đặt Nginx + Certbot
- `docker-compose.https.yml` - Docker stack với HTTPS

## Liên kết hữu ích

- [Caddy Documentation](https://caddyserver.com/docs/)
- [Let's Encrypt Documentation](https://letsencrypt.org/docs/)
- [SSL Labs SSL Test](https://www.ssllabs.com/ssltest/)
- [Mozilla SSL Configuration Generator](https://ssl-config.mozilla.org/)
