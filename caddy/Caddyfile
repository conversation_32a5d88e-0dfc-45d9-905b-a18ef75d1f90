# VSCode Self-Host HTTPS Configuration with <PERSON><PERSON><PERSON>
{$DOMAIN:acb.xyz} {
    # Reverse proxy to code-server container
    reverse_proxy code-server:8080 {
        # WebSocket support for VSCode
        header_up Upgrade {http.request.header.Upgrade}
        header_up Connection {http.request.header.Connection}
        
        # Preserve original request information
        header_up Host {http.request.host}
        header_up X-Real-IP {http.request.remote.host}
        header_up X-Forwarded-For {http.request.remote.host}
        header_up X-Forwarded-Proto {http.request.scheme}
        
        # Health check
        health_uri /healthz
        health_interval 30s
        health_timeout 5s
    }
    
    # Security headers
    header {
        # Enable HSTS (HTTP Strict Transport Security)
        Strict-Transport-Security "max-age=31536000; includeSubDomains; preload"
        
        # Prevent clickjacking attacks
        X-Frame-Options "SAMEORIGIN"
        
        # Prevent MIME type sniffing
        X-Content-Type-Options "nosniff"
        
        # XSS Protection
        X-XSS-Protection "1; mode=block"
        
        # Referrer Policy
        Referrer-Policy "strict-origin-when-cross-origin"
        
        # Content Security Policy (adjust as needed)
        Content-Security-Policy "default-src 'self'; script-src 'self' 'unsafe-inline' 'unsafe-eval'; style-src 'self' 'unsafe-inline'; img-src 'self' data: https:; font-src 'self' data:; connect-src 'self' wss: https:; worker-src 'self' blob:;"
        
        # Remove server information
        -Server
    }
    
    # Rate limiting (optional)
    rate_limit {
        zone static_files {
            key {remote_host}
            events 100
            window 1m
        }
    }
    
    # Logging
    log {
        output file /var/log/caddy/vscode-selfhost.log {
            roll_size 100MB
            roll_keep 5
            roll_keep_for 720h
        }
        format json
        level INFO
    }
    
    # Automatic HTTPS with Let's Encrypt
    tls {$EMAIL:<EMAIL>} {
        protocols tls1.2 tls1.3
    }
    
    # Handle large file uploads
    request_body {
        max_size 100MB
    }
    
    # Compression
    encode {
        gzip 6
        zstd
        minimum_length 1024
        match {
            header Content-Type text/*
            header Content-Type application/json*
            header Content-Type application/javascript*
            header Content-Type application/xhtml+xml*
            header Content-Type application/atom+xml*
            header Content-Type application/rss+xml*
            header Content-Type image/svg+xml*
        }
    }
}

# Redirect HTTP to HTTPS
http://{$DOMAIN:acb.xyz} {
    redir https://{host}{uri} permanent
}

# Health check endpoint
{$DOMAIN:acb.xyz}/health {
    respond "OK" 200
}
