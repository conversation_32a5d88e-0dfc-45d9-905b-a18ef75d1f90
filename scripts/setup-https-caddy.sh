#!/bin/bash

# Script cấu hình HTTPS cho VSCode Self-Host sử dụng Caddy
echo "🔒 <PERSON><PERSON>u hình HTTPS cho VSCode Self-Host với Caddy..."

DOMAIN="${1:-acb.xyz}"
EMAIL="${2:-<EMAIL>}"
CONTAINER_NAME="vscode-selfhost"

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
CYAN='\033[0;36m'
NC='\033[0m' # No Color

# Function to print colored output
print_status() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

print_header() {
    echo -e "${CYAN}$1${NC}"
}

# Check if running as root
check_root() {
    if [ "$EUID" -eq 0 ]; then
        print_error "Không nên chạy script này với quyền root"
        print_status "Hãy chạy với user thường và sử dụng sudo khi cần"
        exit 1
    fi
}

# Install Caddy
install_caddy() {
    print_status "Cài đặt Caddy..."
    
    # Check if Caddy is already installed
    if command -v caddy &> /dev/null; then
        print_success "Caddy đã được cài đặt"
        return 0
    fi
    
    # Install Caddy on Ubuntu/Debian
    if command -v apt &> /dev/null; then
        print_status "Cài đặt Caddy trên Ubuntu/Debian..."
        sudo apt install -y debian-keyring debian-archive-keyring apt-transport-https
        curl -1sLf 'https://dl.cloudsmith.io/public/caddy/stable/gpg.key' | sudo gpg --dearmor -o /usr/share/keyrings/caddy-stable-archive-keyring.gpg
        curl -1sLf 'https://dl.cloudsmith.io/public/caddy/stable/debian.deb.txt' | sudo tee /etc/apt/sources.list.d/caddy-stable.list
        sudo apt update
        sudo apt install -y caddy
    # Install Caddy on CentOS/RHEL/Fedora
    elif command -v yum &> /dev/null || command -v dnf &> /dev/null; then
        print_status "Cài đặt Caddy trên CentOS/RHEL/Fedora..."
        sudo yum install -y yum-plugin-copr
        sudo yum copr enable @caddy/caddy
        sudo yum install -y caddy
    else
        print_error "Hệ điều hành không được hỗ trợ"
        exit 1
    fi
    
    print_success "Caddy đã được cài đặt thành công"
}

# Create Caddyfile
create_caddyfile() {
    print_status "Tạo Caddyfile cho domain $DOMAIN..."
    
    # Create Caddy configuration directory
    sudo mkdir -p /etc/caddy
    
    # Create Caddyfile
    cat << EOF | sudo tee /etc/caddy/Caddyfile > /dev/null
# VSCode Self-Host HTTPS Configuration
$DOMAIN {
    # Reverse proxy to code-server
    reverse_proxy localhost:8008 {
        # WebSocket support
        header_up Upgrade {http.request.header.Upgrade}
        header_up Connection {http.request.header.Connection}
        
        # Preserve original host
        header_up Host {http.request.host}
        header_up X-Real-IP {http.request.remote.host}
        header_up X-Forwarded-For {http.request.remote.host}
        header_up X-Forwarded-Proto {http.request.scheme}
    }
    
    # Security headers
    header {
        # Enable HSTS
        Strict-Transport-Security "max-age=31536000; includeSubDomains; preload"
        
        # Prevent clickjacking
        X-Frame-Options "SAMEORIGIN"
        
        # Prevent MIME type sniffing
        X-Content-Type-Options "nosniff"
        
        # XSS Protection
        X-XSS-Protection "1; mode=block"
        
        # Referrer Policy
        Referrer-Policy "strict-origin-when-cross-origin"
    }
    
    # Logging
    log {
        output file /var/log/caddy/vscode-selfhost.log
        format json
    }
    
    # Automatic HTTPS with Let's Encrypt
    tls $EMAIL
}

# Redirect HTTP to HTTPS
http://$DOMAIN {
    redir https://{host}{uri} permanent
}
EOF
    
    print_success "Caddyfile đã được tạo tại /etc/caddy/Caddyfile"
}

# Configure firewall
configure_firewall() {
    print_status "Cấu hình firewall..."
    
    # Check if ufw is available
    if command -v ufw &> /dev/null; then
        print_status "Cấu hình UFW firewall..."
        sudo ufw allow 80/tcp
        sudo ufw allow 443/tcp
        sudo ufw allow 22/tcp  # SSH
        print_success "UFW firewall đã được cấu hình"
    # Check if firewalld is available
    elif command -v firewall-cmd &> /dev/null; then
        print_status "Cấu hình firewalld..."
        sudo firewall-cmd --permanent --add-service=http
        sudo firewall-cmd --permanent --add-service=https
        sudo firewall-cmd --permanent --add-service=ssh
        sudo firewall-cmd --reload
        print_success "Firewalld đã được cấu hình"
    else
        print_warning "Không tìm thấy firewall manager. Hãy đảm bảo port 80 và 443 được mở"
    fi
}

# Start and enable Caddy
start_caddy() {
    print_status "Khởi động Caddy service..."
    
    # Create log directory
    sudo mkdir -p /var/log/caddy
    sudo chown caddy:caddy /var/log/caddy
    
    # Test Caddyfile syntax
    if sudo caddy validate --config /etc/caddy/Caddyfile; then
        print_success "Caddyfile syntax hợp lệ"
    else
        print_error "Caddyfile syntax không hợp lệ"
        exit 1
    fi
    
    # Start and enable Caddy
    sudo systemctl enable caddy
    sudo systemctl start caddy
    
    # Check status
    if sudo systemctl is-active --quiet caddy; then
        print_success "Caddy service đang chạy"
    else
        print_error "Không thể khởi động Caddy service"
        sudo systemctl status caddy
        exit 1
    fi
}

# Update docker-compose to bind to localhost only
update_docker_compose() {
    print_status "Cập nhật docker-compose để bind localhost only..."
    
    if [ -f "docker-compose.yml" ]; then
        # Backup original file
        cp docker-compose.yml docker-compose.yml.backup.$(date +%Y%m%d_%H%M%S)
        
        # Update port binding to localhost only
        sed -i 's/- "8008:8080"/- "127.0.0.1:8008:8080"/' docker-compose.yml
        
        print_success "Docker-compose đã được cập nhật"
        
        # Restart container
        if docker ps | grep -q "$CONTAINER_NAME"; then
            print_status "Khởi động lại container..."
            docker-compose restart
            sleep 5
            print_success "Container đã được khởi động lại"
        fi
    else
        print_warning "Không tìm thấy docker-compose.yml"
    fi
}

# Verify HTTPS setup
verify_https() {
    print_status "Kiểm tra cấu hình HTTPS..."
    
    # Wait for SSL certificate
    print_status "Đợi SSL certificate được tạo (có thể mất vài phút)..."
    sleep 30
    
    # Test HTTPS connection
    if curl -s -I "https://$DOMAIN" | grep -q "HTTP/2 200"; then
        print_success "HTTPS hoạt động bình thường!"
    else
        print_warning "HTTPS có thể chưa hoạt động. Hãy kiểm tra logs:"
        echo "sudo journalctl -u caddy -f"
    fi
}

# Show final instructions
show_instructions() {
    echo ""
    print_header "🎉 Cấu hình HTTPS hoàn tất!"
    echo "=================================="
    echo ""
    echo "🌐 Truy cập VSCode Self-Host tại:"
    echo "   https://$DOMAIN"
    echo ""
    echo "🔒 Tính năng bảo mật:"
    echo "   ✅ SSL/TLS encryption"
    echo "   ✅ Automatic certificate renewal"
    echo "   ✅ Security headers"
    echo "   ✅ HTTP to HTTPS redirect"
    echo ""
    echo "🛠️  Quản lý Caddy:"
    echo "   • Khởi động lại: sudo systemctl restart caddy"
    echo "   • Xem logs: sudo journalctl -u caddy -f"
    echo "   • Reload config: sudo systemctl reload caddy"
    echo ""
    echo "📁 Files quan trọng:"
    echo "   • Caddyfile: /etc/caddy/Caddyfile"
    echo "   • Logs: /var/log/caddy/vscode-selfhost.log"
    echo ""
    echo "⚠️  Lưu ý:"
    echo "   • Đảm bảo DNS của $DOMAIN trỏ về server này"
    echo "   • Port 80 và 443 phải được mở trên firewall"
    echo "   • Certificate sẽ tự động renew"
    echo ""
}

# Main execution
main() {
    print_header "🚀 Cấu hình HTTPS cho VSCode Self-Host với Caddy"
    echo "=================================================="
    echo ""
    echo "Domain: $DOMAIN"
    echo "Email: $EMAIL"
    echo ""
    
    check_root
    install_caddy
    create_caddyfile
    configure_firewall
    start_caddy
    update_docker_compose
    verify_https
    show_instructions
    
    print_success "Hoàn tất cấu hình HTTPS!"
}

# Show usage if no domain provided
if [ $# -eq 0 ]; then
    echo "Cách sử dụng: $0 <domain> [email]"
    echo "Ví dụ: $0 acb.xyz <EMAIL>"
    exit 1
fi

# Run main function
main
