#!/bin/bash

# Quick HTTPS setup cho domain acb.xyz
echo "⚡ Quick HTTPS Setup cho VSCode Self-Host..."

DOMAIN="acb.xyz"
EMAIL="<EMAIL>"

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
CYAN='\033[0;36m'
NC='\033[0m' # No Color

# Function to print colored output
print_status() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

print_header() {
    echo -e "${CYAN}$1${NC}"
}

# Pre-flight checks
preflight_checks() {
    print_status "Kiểm tra yêu cầu hệ thống..."
    
    # Check if Docker is installed
    if ! command -v docker &> /dev/null; then
        print_error "Docker chưa được cài đặt"
        exit 1
    fi
    
    # Check if Docker Compose is installed
    if ! command -v docker-compose &> /dev/null; then
        print_error "Docker Compose chưa được cài đặt"
        exit 1
    fi
    
    # Check if domain resolves to this server
    print_status "Kiểm tra DNS resolution cho $DOMAIN..."
    local server_ip=$(curl -s ifconfig.me)
    local domain_ip=$(dig +short $DOMAIN | tail -n1)
    
    if [ "$server_ip" = "$domain_ip" ]; then
        print_success "DNS resolution OK: $DOMAIN -> $server_ip"
    else
        print_warning "DNS có thể chưa trỏ đúng: $DOMAIN -> $domain_ip (server: $server_ip)"
        read -p "Bạn có muốn tiếp tục? (y/N): " continue_setup
        if [[ ! $continue_setup =~ ^[Yy]$ ]]; then
            exit 1
        fi
    fi
    
    # Check if ports are available
    if ss -tlnp | grep -q ":80 "; then
        print_warning "Port 80 đang được sử dụng"
    fi
    
    if ss -tlnp | grep -q ":443 "; then
        print_warning "Port 443 đang được sử dụng"
    fi
    
    print_success "Pre-flight checks hoàn tất"
}

# Setup environment
setup_environment() {
    print_status "Cấu hình environment..."
    
    # Create .env file
    cat > .env << EOF
DOMAIN=$DOMAIN
EMAIL=$EMAIL
PASSWORD=admin123
SUDO_PASSWORD=admin123
PROXY_DOMAIN=$DOMAIN
EOF
    
    # Create necessary directories
    mkdir -p caddy/{data,config,logs}
    mkdir -p nginx/{ssl,logs}
    
    # Set proper permissions
    chmod 755 caddy caddy/*
    
    print_success "Environment đã được cấu hình"
}

# Choose setup method
choose_setup_method() {
    echo ""
    print_header "🚀 Chọn phương pháp cấu hình HTTPS:"
    echo "=================================="
    echo ""
    echo "1. 🐳 Docker Compose (Khuyến nghị)"
    echo "   • Dễ quản lý và triển khai"
    echo "   • Tự động SSL với Caddy"
    echo "   • Isolated environment"
    echo ""
    echo "2. 🖥️  System Services (Caddy)"
    echo "   • Cài đặt trực tiếp trên hệ thống"
    echo "   • Performance tốt hơn"
    echo "   • Quản lý qua systemd"
    echo ""
    echo "3. 🖥️  System Services (Nginx)"
    echo "   • Sử dụng Nginx + Certbot"
    echo "   • Cấu hình linh hoạt"
    echo "   • Phù hợp với infrastructure có sẵn"
    echo ""
    
    read -p "Chọn phương pháp (1-3): " method
    
    case $method in
        1)
            setup_docker_https
            ;;
        2)
            setup_caddy_system
            ;;
        3)
            setup_nginx_system
            ;;
        *)
            print_error "Lựa chọn không hợp lệ"
            exit 1
            ;;
    esac
}

# Setup Docker HTTPS
setup_docker_https() {
    print_status "Cấu hình HTTPS với Docker Compose..."
    
    # Stop existing containers
    if docker ps | grep -q "vscode-selfhost"; then
        print_status "Dừng containers hiện tại..."
        docker-compose down
    fi
    
    # Start HTTPS stack
    print_status "Khởi động HTTPS stack..."
    docker-compose -f docker-compose.https.yml up -d
    
    if [ $? -eq 0 ]; then
        print_success "HTTPS stack đã được khởi động"
        
        # Wait for services
        print_status "Đợi services khởi động và SSL certificate được tạo..."
        sleep 60
        
        # Verify setup
        verify_https_setup
    else
        print_error "Không thể khởi động HTTPS stack"
        docker-compose -f docker-compose.https.yml logs
        exit 1
    fi
}

# Setup Caddy system service
setup_caddy_system() {
    print_status "Cấu hình HTTPS với Caddy system service..."
    
    if [ -f "scripts/setup-https-caddy.sh" ]; then
        chmod +x scripts/setup-https-caddy.sh
        ./scripts/setup-https-caddy.sh "$DOMAIN" "$EMAIL"
    else
        print_error "Script setup-https-caddy.sh không tồn tại"
        exit 1
    fi
}

# Setup Nginx system service
setup_nginx_system() {
    print_status "Cấu hình HTTPS với Nginx system service..."
    
    if [ -f "scripts/setup-https-nginx.sh" ]; then
        chmod +x scripts/setup-https-nginx.sh
        ./scripts/setup-https-nginx.sh "$DOMAIN" "$EMAIL"
    else
        print_error "Script setup-https-nginx.sh không tồn tại"
        exit 1
    fi
}

# Verify HTTPS setup
verify_https_setup() {
    print_status "Kiểm tra cấu hình HTTPS..."
    
    # Test HTTPS connection
    local max_attempts=12
    local attempt=1
    
    while [ $attempt -le $max_attempts ]; do
        print_status "Thử kết nối HTTPS (lần $attempt/$max_attempts)..."
        
        if curl -s -I "https://$DOMAIN" | grep -q "HTTP/2 200\|HTTP/1.1 200"; then
            print_success "✅ HTTPS hoạt động bình thường!"
            
            # Check SSL certificate
            local cert_info=$(echo | openssl s_client -servername "$DOMAIN" -connect "$DOMAIN:443" 2>/dev/null | openssl x509 -noout -subject -issuer 2>/dev/null)
            if [ -n "$cert_info" ]; then
                echo "📋 SSL Certificate:"
                echo "$cert_info"
            fi
            
            return 0
        else
            print_warning "HTTPS chưa hoạt động, đợi 10 giây..."
            sleep 10
            ((attempt++))
        fi
    done
    
    print_error "HTTPS không hoạt động sau $max_attempts lần thử"
    print_status "Hãy kiểm tra logs để debug:"
    
    if docker ps | grep -q "vscode-caddy"; then
        echo "docker logs vscode-caddy"
    elif systemctl is-active --quiet caddy 2>/dev/null; then
        echo "sudo journalctl -u caddy -f"
    elif systemctl is-active --quiet nginx 2>/dev/null; then
        echo "sudo tail -f /var/log/nginx/error.log"
    fi
    
    return 1
}

# Show final instructions
show_final_instructions() {
    echo ""
    print_header "🎉 HTTPS Setup hoàn tất!"
    echo "========================="
    echo ""
    echo "🌐 Truy cập VSCode Self-Host tại:"
    echo "   https://$DOMAIN"
    echo ""
    echo "🔒 Tính năng bảo mật:"
    echo "   ✅ SSL/TLS encryption"
    echo "   ✅ Automatic certificate renewal"
    echo "   ✅ Security headers"
    echo "   ✅ HTTP to HTTPS redirect"
    echo ""
    echo "🛠️  Quản lý HTTPS:"
    echo "   • HTTPS Manager: ./scripts/https-manager.sh"
    echo "   • Marketplace Manager: ./scripts/marketplace-manager.sh"
    echo "   • Troubleshoot: ./scripts/troubleshoot-copilot.sh"
    echo ""
    echo "📚 Tài liệu:"
    echo "   • HTTPS Guide: docs/https-configuration.md"
    echo "   • Marketplace Guide: docs/marketplace-configuration.md"
    echo ""
    echo "⚠️  Lưu ý quan trọng:"
    echo "   • Backup cấu hình thường xuyên"
    echo "   • Monitor SSL certificate expiry"
    echo "   • Kiểm tra logs định kỳ"
    echo ""
    
    # Test final connection
    if curl -s -I "https://$DOMAIN" >/dev/null 2>&1; then
        print_success "🚀 VSCode Self-Host đã sẵn sàng với HTTPS!"
    else
        print_warning "⚠️  HTTPS có thể cần thêm thời gian để hoạt động"
    fi
}

# Main execution
main() {
    print_header "⚡ Quick HTTPS Setup cho VSCode Self-Host"
    echo "========================================"
    echo "Domain: $DOMAIN"
    echo "Email: $EMAIL"
    echo ""
    
    preflight_checks
    setup_environment
    choose_setup_method
    show_final_instructions
    
    print_success "Setup hoàn tất!"
}

# Run main function
main
