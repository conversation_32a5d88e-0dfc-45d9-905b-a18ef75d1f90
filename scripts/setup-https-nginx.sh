#!/bin/bash

# Script cấu hình HTTPS cho VSCode Self-Host sử dụng Nginx
echo "🔒 <PERSON><PERSON><PERSON> hình HTTPS cho VSCode Self-Host với Nginx..."

DOMAIN="${1:-acb.xyz}"
EMAIL="${2:-<EMAIL>}"
CONTAINER_NAME="vscode-selfhost"

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
CYAN='\033[0;36m'
NC='\033[0m' # No Color

# Function to print colored output
print_status() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

print_header() {
    echo -e "${CYAN}$1${NC}"
}

# Check if running as root
check_root() {
    if [ "$EUID" -eq 0 ]; then
        print_error "Không nên chạy script này với quyền root"
        print_status "Hãy chạy với user thường và sử dụng sudo khi cần"
        exit 1
    fi
}

# Install Nginx and Certbot
install_nginx_certbot() {
    print_status "Cài đặt Nginx và Certbot..."
    
    # Update package list
    sudo apt update
    
    # Install Nginx, Certbot, and Certbot Nginx plugin
    sudo apt install -y nginx certbot python3-certbot-nginx
    
    print_success "Nginx và Certbot đã được cài đặt"
}

# Create Nginx configuration
create_nginx_config() {
    print_status "Tạo cấu hình Nginx cho domain $DOMAIN..."
    
    # Create Nginx site configuration
    cat << EOF | sudo tee /etc/nginx/sites-available/vscode-selfhost > /dev/null
# VSCode Self-Host HTTPS Configuration
server {
    listen 80;
    listen [::]:80;
    server_name $DOMAIN;
    
    # Redirect HTTP to HTTPS
    return 301 https://\$server_name\$request_uri;
}

server {
    listen 443 ssl http2;
    listen [::]:443 ssl http2;
    server_name $DOMAIN;
    
    # SSL Configuration (will be managed by Certbot)
    ssl_certificate /etc/letsencrypt/live/$DOMAIN/fullchain.pem;
    ssl_certificate_key /etc/letsencrypt/live/$DOMAIN/privkey.pem;
    include /etc/letsencrypt/options-ssl-nginx.conf;
    ssl_dhparam /etc/letsencrypt/ssl-dhparams.pem;
    
    # Security Headers
    add_header Strict-Transport-Security "max-age=31536000; includeSubDomains; preload" always;
    add_header X-Frame-Options "SAMEORIGIN" always;
    add_header X-Content-Type-Options "nosniff" always;
    add_header X-XSS-Protection "1; mode=block" always;
    add_header Referrer-Policy "strict-origin-when-cross-origin" always;
    
    # Proxy to code-server
    location / {
        proxy_pass http://127.0.0.1:8008;
        proxy_set_header Host \$http_host;
        proxy_set_header X-Real-IP \$remote_addr;
        proxy_set_header X-Forwarded-For \$proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto \$scheme;
        
        # WebSocket support
        proxy_set_header Upgrade \$http_upgrade;
        proxy_set_header Connection upgrade;
        proxy_set_header Accept-Encoding gzip;
        
        # Timeouts
        proxy_connect_timeout 60s;
        proxy_send_timeout 60s;
        proxy_read_timeout 60s;
        
        # Buffer settings
        proxy_buffering off;
        proxy_request_buffering off;
        proxy_http_version 1.1;
        
        # Large file uploads
        client_max_body_size 100M;
    }
    
    # Logging
    access_log /var/log/nginx/vscode-selfhost.access.log;
    error_log /var/log/nginx/vscode-selfhost.error.log;
}
EOF
    
    print_success "Cấu hình Nginx đã được tạo"
}

# Enable Nginx site
enable_nginx_site() {
    print_status "Kích hoạt site Nginx..."
    
    # Remove default site if exists
    sudo rm -f /etc/nginx/sites-enabled/default
    
    # Enable our site
    sudo ln -sf /etc/nginx/sites-available/vscode-selfhost /etc/nginx/sites-enabled/
    
    # Test Nginx configuration
    if sudo nginx -t; then
        print_success "Cấu hình Nginx hợp lệ"
    else
        print_error "Cấu hình Nginx không hợp lệ"
        exit 1
    fi
    
    # Restart Nginx
    sudo systemctl restart nginx
    sudo systemctl enable nginx
    
    print_success "Nginx đã được kích hoạt"
}

# Configure firewall
configure_firewall() {
    print_status "Cấu hình firewall..."
    
    # Check if ufw is available
    if command -v ufw &> /dev/null; then
        print_status "Cấu hình UFW firewall..."
        sudo ufw allow 'Nginx Full'
        sudo ufw allow 22/tcp  # SSH
        print_success "UFW firewall đã được cấu hình"
    # Check if firewalld is available
    elif command -v firewall-cmd &> /dev/null; then
        print_status "Cấu hình firewalld..."
        sudo firewall-cmd --permanent --add-service=http
        sudo firewall-cmd --permanent --add-service=https
        sudo firewall-cmd --permanent --add-service=ssh
        sudo firewall-cmd --reload
        print_success "Firewalld đã được cấu hình"
    else
        print_warning "Không tìm thấy firewall manager. Hãy đảm bảo port 80 và 443 được mở"
    fi
}

# Obtain SSL certificate
obtain_ssl_certificate() {
    print_status "Lấy SSL certificate từ Let's Encrypt..."
    
    # Run Certbot
    sudo certbot --nginx --non-interactive --agree-tos --redirect \
        -d "$DOMAIN" -m "$EMAIL"
    
    if [ $? -eq 0 ]; then
        print_success "SSL certificate đã được cài đặt thành công"
    else
        print_error "Không thể lấy SSL certificate"
        print_status "Hãy kiểm tra:"
        echo "  • DNS của $DOMAIN có trỏ về server này không"
        echo "  • Port 80 và 443 có được mở không"
        echo "  • Domain có thể truy cập từ internet không"
        exit 1
    fi
}

# Setup automatic certificate renewal
setup_auto_renewal() {
    print_status "Cấu hình tự động gia hạn certificate..."
    
    # Test renewal
    sudo certbot renew --dry-run
    
    if [ $? -eq 0 ]; then
        print_success "Tự động gia hạn certificate đã được cấu hình"
    else
        print_warning "Có vấn đề với tự động gia hạn certificate"
    fi
}

# Update docker-compose to bind to localhost only
update_docker_compose() {
    print_status "Cập nhật docker-compose để bind localhost only..."
    
    if [ -f "docker-compose.yml" ]; then
        # Backup original file
        cp docker-compose.yml docker-compose.yml.backup.$(date +%Y%m%d_%H%M%S)
        
        # Update port binding to localhost only
        sed -i 's/- "8008:8080"/- "127.0.0.1:8008:8080"/' docker-compose.yml
        
        print_success "Docker-compose đã được cập nhật"
        
        # Restart container
        if docker ps | grep -q "$CONTAINER_NAME"; then
            print_status "Khởi động lại container..."
            docker-compose restart
            sleep 5
            print_success "Container đã được khởi động lại"
        fi
    else
        print_warning "Không tìm thấy docker-compose.yml"
    fi
}

# Verify HTTPS setup
verify_https() {
    print_status "Kiểm tra cấu hình HTTPS..."
    
    # Test HTTPS connection
    if curl -s -I "https://$DOMAIN" | grep -q "HTTP/2 200"; then
        print_success "HTTPS hoạt động bình thường!"
    else
        print_warning "HTTPS có thể chưa hoạt động. Hãy kiểm tra logs:"
        echo "sudo tail -f /var/log/nginx/vscode-selfhost.error.log"
    fi
}

# Show final instructions
show_instructions() {
    echo ""
    print_header "🎉 Cấu hình HTTPS hoàn tất!"
    echo "=================================="
    echo ""
    echo "🌐 Truy cập VSCode Self-Host tại:"
    echo "   https://$DOMAIN"
    echo ""
    echo "🔒 Tính năng bảo mật:"
    echo "   ✅ SSL/TLS encryption"
    echo "   ✅ Automatic certificate renewal"
    echo "   ✅ Security headers"
    echo "   ✅ HTTP to HTTPS redirect"
    echo ""
    echo "🛠️  Quản lý Nginx:"
    echo "   • Khởi động lại: sudo systemctl restart nginx"
    echo "   • Xem logs: sudo tail -f /var/log/nginx/vscode-selfhost.error.log"
    echo "   • Test config: sudo nginx -t"
    echo ""
    echo "🔐 Quản lý SSL Certificate:"
    echo "   • Gia hạn thủ công: sudo certbot renew"
    echo "   • Xem certificates: sudo certbot certificates"
    echo "   • Test renewal: sudo certbot renew --dry-run"
    echo ""
    echo "📁 Files quan trọng:"
    echo "   • Nginx config: /etc/nginx/sites-available/vscode-selfhost"
    echo "   • SSL certificates: /etc/letsencrypt/live/$DOMAIN/"
    echo "   • Access logs: /var/log/nginx/vscode-selfhost.access.log"
    echo "   • Error logs: /var/log/nginx/vscode-selfhost.error.log"
    echo ""
    echo "⚠️  Lưu ý:"
    echo "   • Đảm bảo DNS của $DOMAIN trỏ về server này"
    echo "   • Certificate sẽ tự động gia hạn mỗi 60 ngày"
    echo "   • Backup cấu hình trước khi thay đổi"
    echo ""
}

# Main execution
main() {
    print_header "🚀 Cấu hình HTTPS cho VSCode Self-Host với Nginx"
    echo "=================================================="
    echo ""
    echo "Domain: $DOMAIN"
    echo "Email: $EMAIL"
    echo ""
    
    check_root
    install_nginx_certbot
    create_nginx_config
    enable_nginx_site
    configure_firewall
    obtain_ssl_certificate
    setup_auto_renewal
    update_docker_compose
    verify_https
    show_instructions
    
    print_success "Hoàn tất cấu hình HTTPS!"
}

# Show usage if no domain provided
if [ $# -eq 0 ]; then
    echo "Cách sử dụng: $0 <domain> [email]"
    echo "Ví dụ: $0 acb.xyz <EMAIL>"
    exit 1
fi

# Run main function
main
