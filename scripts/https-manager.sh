#!/bin/bash

# Script quản lý HTTPS cho VSCode Self-Host
echo "🔒 Quản lý HTTPS cho VSCode Self-Host..."

DOMAIN="${DOMAIN:-acb.xyz}"
EMAIL="${EMAIL:-<EMAIL>}"

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
CYAN='\033[0;36m'
NC='\033[0m' # No Color

# Function to print colored output
print_status() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

print_header() {
    echo -e "${CYAN}$1${NC}"
}

# Check current HTTPS status
check_https_status() {
    print_status "Kiểm tra trạng thái HTTPS..."
    
    # Check if domain is accessible via HTTPS
    if curl -s -I "https://$DOMAIN" >/dev/null 2>&1; then
        print_success "✅ HTTPS đang hoạt động cho $DOMAIN"
        
        # Check SSL certificate details
        local cert_info=$(echo | openssl s_client -servername "$DOMAIN" -connect "$DOMAIN:443" 2>/dev/null | openssl x509 -noout -dates 2>/dev/null)
        if [ -n "$cert_info" ]; then
            echo "📋 Thông tin SSL Certificate:"
            echo "$cert_info"
        fi
        return 0
    else
        print_warning "❌ HTTPS chưa hoạt động cho $DOMAIN"
        return 1
    fi
}

# Check reverse proxy status
check_reverse_proxy() {
    print_status "Kiểm tra reverse proxy..."
    
    # Check if Caddy is running
    if docker ps | grep -q "vscode-caddy"; then
        print_success "✅ Caddy reverse proxy đang chạy"
        return 0
    elif systemctl is-active --quiet caddy 2>/dev/null; then
        print_success "✅ Caddy system service đang chạy"
        return 0
    elif systemctl is-active --quiet nginx 2>/dev/null; then
        print_success "✅ Nginx system service đang chạy"
        return 0
    else
        print_warning "❌ Không tìm thấy reverse proxy đang chạy"
        return 1
    fi
}

# Setup HTTPS with Docker Compose
setup_https_docker() {
    print_status "Cấu hình HTTPS với Docker Compose..."
    
    # Create environment file
    cat > .env << EOF
DOMAIN=$DOMAIN
EMAIL=$EMAIL
PASSWORD=admin123
SUDO_PASSWORD=admin123
PROXY_DOMAIN=$DOMAIN
EOF
    
    print_success "Đã tạo file .env"
    
    # Create necessary directories
    mkdir -p caddy/{data,config,logs}
    mkdir -p nginx/{ssl,logs}
    
    # Start HTTPS stack
    print_status "Khởi động HTTPS stack..."
    docker-compose -f docker-compose.https.yml up -d
    
    if [ $? -eq 0 ]; then
        print_success "HTTPS stack đã được khởi động"
        
        # Wait for services to be ready
        print_status "Đợi services khởi động (30 giây)..."
        sleep 30
        
        # Check if services are running
        if docker ps | grep -q "vscode-caddy" && docker ps | grep -q "vscode-selfhost"; then
            print_success "Tất cả services đang chạy"
        else
            print_error "Một số services không khởi động được"
            docker-compose -f docker-compose.https.yml logs
        fi
    else
        print_error "Không thể khởi động HTTPS stack"
        exit 1
    fi
}

# Setup HTTPS with system services
setup_https_system() {
    print_status "Chọn phương pháp cấu hình HTTPS system:"
    echo "1. Caddy (Khuyến nghị - Tự động SSL)"
    echo "2. Nginx + Certbot"
    echo "3. Quay lại"
    
    read -p "Chọn (1-3): " choice
    
    case $choice in
        1)
            print_status "Cấu hình HTTPS với Caddy..."
            ./scripts/setup-https-caddy.sh "$DOMAIN" "$EMAIL"
            ;;
        2)
            print_status "Cấu hình HTTPS với Nginx..."
            ./scripts/setup-https-nginx.sh "$DOMAIN" "$EMAIL"
            ;;
        3)
            return
            ;;
        *)
            print_error "Lựa chọn không hợp lệ"
            ;;
    esac
}

# Manage SSL certificates
manage_ssl_certificates() {
    print_status "Quản lý SSL Certificates..."
    echo ""
    echo "1. Xem thông tin certificates"
    echo "2. Gia hạn certificates"
    echo "3. Test gia hạn certificates"
    echo "4. Backup certificates"
    echo "5. Quay lại"
    
    read -p "Chọn (1-5): " choice
    
    case $choice in
        1)
            print_status "Thông tin SSL certificates:"
            if command -v certbot >/dev/null 2>&1; then
                sudo certbot certificates
            else
                print_warning "Certbot không được cài đặt"
            fi
            ;;
        2)
            print_status "Gia hạn SSL certificates..."
            if command -v certbot >/dev/null 2>&1; then
                sudo certbot renew
            else
                print_warning "Certbot không được cài đặt"
            fi
            ;;
        3)
            print_status "Test gia hạn SSL certificates..."
            if command -v certbot >/dev/null 2>&1; then
                sudo certbot renew --dry-run
            else
                print_warning "Certbot không được cài đặt"
            fi
            ;;
        4)
            print_status "Backup SSL certificates..."
            if [ -d "/etc/letsencrypt" ]; then
                sudo tar -czf "ssl-backup-$(date +%Y%m%d_%H%M%S).tar.gz" /etc/letsencrypt
                print_success "SSL certificates đã được backup"
            else
                print_warning "Không tìm thấy thư mục certificates"
            fi
            ;;
        5)
            return
            ;;
        *)
            print_error "Lựa chọn không hợp lệ"
            ;;
    esac
}

# Troubleshoot HTTPS issues
troubleshoot_https() {
    print_status "Troubleshoot HTTPS issues..."
    echo ""
    
    # Check DNS resolution
    print_status "Kiểm tra DNS resolution..."
    if nslookup "$DOMAIN" >/dev/null 2>&1; then
        print_success "DNS resolution OK"
    else
        print_error "DNS resolution failed"
    fi
    
    # Check port connectivity
    print_status "Kiểm tra port connectivity..."
    if nc -z "$DOMAIN" 443 2>/dev/null; then
        print_success "Port 443 accessible"
    else
        print_error "Port 443 not accessible"
    fi
    
    # Check SSL certificate
    print_status "Kiểm tra SSL certificate..."
    echo | openssl s_client -servername "$DOMAIN" -connect "$DOMAIN:443" 2>/dev/null | openssl x509 -noout -text | grep -E "(Subject|Issuer|Not Before|Not After)"
    
    # Check logs
    print_status "Logs gần đây:"
    if docker ps | grep -q "vscode-caddy"; then
        echo "=== Caddy Logs ==="
        docker logs --tail 20 vscode-caddy
    elif systemctl is-active --quiet caddy 2>/dev/null; then
        echo "=== Caddy System Logs ==="
        sudo journalctl -u caddy --no-pager -n 20
    elif systemctl is-active --quiet nginx 2>/dev/null; then
        echo "=== Nginx Logs ==="
        sudo tail -20 /var/log/nginx/error.log
    fi
}

# Show main menu
show_menu() {
    echo ""
    print_header "🛠️  HTTPS Management Menu"
    echo "=========================="
    echo "1. Kiểm tra trạng thái HTTPS"
    echo "2. Cấu hình HTTPS với Docker"
    echo "3. Cấu hình HTTPS với System Services"
    echo "4. Quản lý SSL Certificates"
    echo "5. Troubleshoot HTTPS"
    echo "6. Xem logs"
    echo "7. Thoát"
    echo ""
    read -p "Chọn tùy chọn (1-7): " choice
    
    case $choice in
        1)
            check_https_status
            check_reverse_proxy
            ;;
        2)
            setup_https_docker
            ;;
        3)
            setup_https_system
            ;;
        4)
            manage_ssl_certificates
            ;;
        5)
            troubleshoot_https
            ;;
        6)
            print_status "Xem logs..."
            if docker ps | grep -q "vscode-caddy"; then
                docker logs -f vscode-caddy
            elif systemctl is-active --quiet caddy 2>/dev/null; then
                sudo journalctl -u caddy -f
            elif systemctl is-active --quiet nginx 2>/dev/null; then
                sudo tail -f /var/log/nginx/vscode-selfhost.error.log
            else
                print_warning "Không tìm thấy service nào để xem logs"
            fi
            ;;
        7)
            print_success "Tạm biệt!"
            exit 0
            ;;
        *)
            print_error "Lựa chọn không hợp lệ. Vui lòng chọn 1-7."
            ;;
    esac
}

# Show current status
show_status() {
    echo ""
    print_header "🔒 VSCode Self-Host HTTPS Manager"
    echo "================================="
    echo "Domain: $DOMAIN"
    echo "Email: $EMAIL"
    echo ""
    
    # Quick status check
    check_https_status
    check_reverse_proxy
}

# Main execution
main() {
    show_status
    
    # Main menu loop
    while true; do
        show_menu
        echo ""
        read -p "Nhấn Enter để tiếp tục hoặc Ctrl+C để thoát..."
    done
}

# Run main function
main
